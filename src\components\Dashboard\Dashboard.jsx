import React, { useState, useEffect } from 'react';
import { 
  User, 
  LogOut, 
  Plus, 
  Eye, 
  Trash2, 
  TrendingUp, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  Activity,
  Calendar,
  BarChart3,
  Coins,
  RefreshCw,
  ChevronLeft,
  ChevronRight,
  FileText,
  Wifi,
  WifiOff
} from 'lucide-react';

// Mock data for demonstration
const mockUser = {
  email: '<EMAIL>',
  token_balance: 150
};

const mockStats = {
  total_analyses: 24,
  completed: 18,
  processing: 3,
  failed: 3
};

const mockResults = [
  {
    id: 1,
    created_at: '2024-01-15T10:30:00Z',
    status: 'completed'
  },
  {
    id: 2,
    created_at: '2024-01-14T14:20:00Z',
    status: 'processing'
  },
  {
    id: 3,
    created_at: '2024-01-13T09:15:00Z',
    status: 'failed'
  },
  {
    id: 4,
    created_at: '2024-01-12T16:45:00Z',
    status: 'completed'
  }
];

const mockPagination = {
  page: 1,
  limit: 10,
  total: 24,
  totalPages: 3
};

// Connection Status Component
const ConnectionStatus = ({ isConnected, isAuthenticated }) => (
  <div className="flex items-center space-x-2">
    <div className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${
      isConnected && isAuthenticated 
        ? 'bg-green-100 text-green-800' 
        : 'bg-red-100 text-red-800'
    }`}>
      {isConnected && isAuthenticated ? (
        <>
          <Wifi className="h-3 w-3" />
          <span>Connected</span>
        </>
      ) : (
        <>
          <WifiOff className="h-3 w-3" />
          <span>Disconnected</span>
        </>
      )}
    </div>
  </div>
);

// Delete Modal Component
const DeleteResultModal = ({ isOpen, onClose, result, onDeleted }) => {
  if (!isOpen) return null;

  const handleDelete = () => {
    onDeleted(result.id);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl p-6 max-w-md w-full mx-4 shadow-2xl">
        <div className="flex items-center space-x-3 mb-4">
          <div className="p-2 bg-red-100 rounded-full">
            <AlertCircle className="h-6 w-6 text-red-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Delete Assessment</h3>
            <p className="text-sm text-gray-500">This action cannot be undone</p>
          </div>
        </div>
        
        <p className="text-gray-700 mb-6">
          Are you sure you want to delete this assessment result?
        </p>
        
        <div className="flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleDelete}
            className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            Delete
          </button>
        </div>
      </div>
    </div>
  );
};

export default function ImprovedDashboard() {
  const [results, setResults] = useState(mockResults);
  const [stats, setStats] = useState(mockStats);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pagination, setPagination] = useState(mockPagination);
  const [deleteModal, setDeleteModal] = useState({ isOpen: false, result: null });

  // Mock connection status
  const [isConnected, setIsConnected] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(true);

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusBadge = (status) => {
    const configs = {
      completed: {
        icon: CheckCircle,
        classes: 'bg-green-100 text-green-800 border-green-200',
        label: 'Completed'
      },
      processing: {
        icon: Clock,
        classes: 'bg-blue-100 text-blue-800 border-blue-200',
        label: 'Processing'
      },
      failed: {
        icon: AlertCircle,
        classes: 'bg-red-100 text-red-800 border-red-200',
        label: 'Failed'
      }
    };

    const config = configs[status] || configs.completed;
    const Icon = config.icon;

    return (
      <span className={`inline-flex items-center space-x-1 px-2 py-1 text-xs font-medium rounded-full border ${config.classes}`}>
        <Icon className="h-3 w-3" />
        <span>{config.label}</span>
      </span>
    );
  };

  const handleViewResult = (resultId) => {
    console.log('Navigating to result:', resultId);
  };

  const handleDeleteResult = (result) => {
    setDeleteModal({ isOpen: true, result });
  };

  const handleDeleteConfirmed = (deletedResultId) => {
    setResults(prev => prev.filter(result => result.id !== deletedResultId));
    setDeleteModal({ isOpen: false, result: null });
    // Update stats
    setStats(prev => ({
      ...prev,
      total_analyses: prev.total_analyses - 1
    }));
  };

  const handleCloseDeleteModal = () => {
    setDeleteModal({ isOpen: false, result: null });
  };

  const handleLogout = () => {
    console.log('Logging out...');
  };

  const handleNewAssessment = () => {
    console.log('Starting new assessment...');
  };

  const handleProfile = () => {
    console.log('Opening profile...');
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-indigo-50">
        <div className="text-center">
          <div className="relative">
            <div className="animate-spin rounded-full h-16 w-16 border-4 border-indigo-200 border-t-indigo-600 mx-auto"></div>
            <div className="absolute inset-0 flex items-center justify-center">
              <BarChart3 className="h-6 w-6 text-indigo-600" />
            </div>
          </div>
          <h2 className="mt-4 text-xl font-semibold text-gray-900">Loading Dashboard...</h2>
          <p className="text-sm text-gray-500 mt-1">Please wait while we fetch your data</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-indigo-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-indigo-100 rounded-xl">
                <BarChart3 className="h-8 w-8 text-indigo-600" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
                <p className="text-sm text-gray-600 flex items-center space-x-1">
                  <span>Welcome back,</span>
                  <span className="font-medium text-indigo-600">{mockUser.email}</span>
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <ConnectionStatus
                isConnected={isConnected}
                isAuthenticated={isAuthenticated}
              />
              
              <button
                onClick={handleProfile}
                className="flex items-center space-x-2 px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                <User className="h-4 w-4" />
                <span>Profile</span>
              </button>
              
              <button
                onClick={handleLogout}
                className="flex items-center space-x-2 px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                <LogOut className="h-4 w-4" />
                <span>Logout</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Cards - Horizontal Layout */}
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow mb-8">
          <div className="flex flex-wrap items-center justify-between gap-6">
            {/* Total Analyses */}
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-indigo-100 rounded-lg">
                <BarChart3 className="h-6 w-6 text-indigo-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Total Analyses</p>
                <p className="text-2xl font-bold text-gray-900 mt-1">{stats.total_analyses}</p>
              </div>
            </div>
            
            {/* Completed */}
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-green-100 rounded-lg">
                <CheckCircle className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Completed</p>
                <p className="text-2xl font-bold text-green-600 mt-1">{stats.completed}</p>
              </div>
            </div>
            
            {/* Processing */}
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-blue-100 rounded-lg">
                <Clock className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Processing</p>
                <p className="text-2xl font-bold text-blue-600 mt-1">{stats.processing}</p>
              </div>
            </div>
            
            {/* Token Balance */}
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-purple-100 rounded-lg">
                <Coins className="h-6 w-6 text-purple-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Token Balance</p>
                <p className="text-2xl font-bold text-purple-600 mt-1">{mockUser.token_balance}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-xl p-4 mb-6">
            <div className="flex items-center space-x-2">
              <AlertCircle className="h-5 w-5 text-red-500" />
              <div>
                <h3 className="text-sm font-medium text-red-800">Error</h3>
                <p className="text-sm text-red-700 mt-1">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Assessment History */}
        <div className="bg-white shadow-sm rounded-xl border border-gray-100">
          <div className="px-6 py-4 border-b border-gray-100">
            <div className="flex justify-between items-center">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-gray-100 rounded-lg">
                  <FileText className="h-5 w-5 text-gray-600" />
                </div>
                <div>
                  <h2 className="text-xl font-semibold text-gray-900">Assessment History</h2>
                  <p className="text-sm text-gray-500">Track your assessment progress and results</p>
                </div>
              </div>
              
              <button
                onClick={handleNewAssessment}
                className="flex items-center space-x-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors shadow-sm"
              >
                <Plus className="h-4 w-4" />
                <span>New Assessment</span>
              </button>
            </div>
          </div>
          
          {results.length === 0 ? (
            <div className="text-center py-16">
              <div className="p-4 bg-gray-100 rounded-full w-16 h-16 mx-auto mb-4">
                <FileText className="h-8 w-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No assessments yet</h3>
              <p className="text-gray-500 mb-6">Your assessment results will appear here once completed.</p>
              <button
                onClick={handleNewAssessment}
                className="flex items-center space-x-2 px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors mx-auto"
              >
                <Plus className="h-4 w-4" />
                <span>Start Your First Assessment</span>
              </button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-100">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-4 w-4" />
                        <span>Date & Time</span>
                      </div>
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      <div className="flex items-center space-x-1">
                        <Activity className="h-4 w-4" />
                        <span>Status</span>
                      </div>
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-100">
                  {results.map((result) => (
                    <tr key={result.id} className="hover:bg-gray-50 transition-colors">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {formatDate(result.created_at)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(result.status)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center space-x-3">
                          {result.status === 'completed' ? (
                            <button
                              onClick={() => handleViewResult(result.id)}
                              className="flex items-center space-x-1 text-indigo-600 hover:text-indigo-800 font-medium text-sm transition-colors"
                            >
                              <Eye className="h-4 w-4" />
                              <span>View Results</span>
                            </button>
                          ) : result.status === 'processing' ? (
                            <div className="flex items-center space-x-1 text-blue-600">
                              <RefreshCw className="h-4 w-4 animate-spin" />
                              <span className="text-sm">Processing...</span>
                            </div>
                          ) : (
                            <span className="text-sm text-gray-500">Failed</span>
                          )}
                          
                          <button
                            onClick={() => handleDeleteResult(result)}
                            className="flex items-center space-x-1 text-red-600 hover:text-red-800 font-medium text-sm transition-colors"
                          >
                            <Trash2 className="h-4 w-4" />
                            <span>Delete</span>
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {/* Pagination */}
          {pagination && pagination.totalPages > 1 && (
            <div className="px-6 py-4 border-t border-gray-100 bg-gray-50">
              <div className="flex justify-between items-center">
                <div className="text-sm text-gray-600">
                  Showing {((pagination.page - 1) * pagination.limit) + 1} to {Math.min(pagination.page * pagination.limit, pagination.total)} of {pagination.total} results
                </div>
                
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                    disabled={pagination.page === 1}
                    className="flex items-center space-x-1 px-3 py-2 text-sm font-medium text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    <ChevronLeft className="h-4 w-4" />
                    <span>Previous</span>
                  </button>
                  
                  <div className="flex items-center space-x-1">
                    {Array.from({ length: Math.min(pagination.totalPages, 5) }, (_, i) => (
                      <button
                        key={i + 1}
                        onClick={() => setCurrentPage(i + 1)}
                        className={`px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                          pagination.page === i + 1
                            ? 'bg-indigo-600 text-white'
                            : 'text-gray-600 bg-white border border-gray-300 hover:bg-gray-50'
                        }`}
                      >
                        {i + 1}
                      </button>
                    ))}
                  </div>
                  
                  <button
                    onClick={() => setCurrentPage(prev => Math.min(prev + 1, pagination.totalPages))}
                    disabled={pagination.page === pagination.totalPages}
                    className="flex items-center space-x-1 px-3 py-2 text-sm font-medium text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    <span>Next</span>
                    <ChevronRight className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Delete Result Modal */}
      <DeleteResultModal
        isOpen={deleteModal.isOpen}
        onClose={handleCloseDeleteModal}
        result={deleteModal.result}
        onDeleted={handleDeleteConfirmed}
      />
    </div>
  );
};

export default Dashboard;
