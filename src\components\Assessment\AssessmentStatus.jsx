import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import apiService from '../../services/apiService';
import { useNotifications } from '../../hooks/useNotifications';
import LoadingSpinner from '../UI/LoadingSpinner';
import ErrorMessage from '../UI/ErrorMessage';
import ProgressBar from '../UI/ProgressBar';
import ConnectionStatus from '../UI/ConnectionStatus';

const AssessmentStatus = () => {
  const { jobId } = useParams();
  const navigate = useNavigate();
  const [status, setStatus] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [pollingInterval, setPollingInterval] = useState(null);

  // Setup notifications with callbacks
  const { isConnected, isAuthenticated, notifications, clearNotification } = useNotifications({
    onAnalysisComplete: (data) => {
      if (data.jobId === jobId) {
        // Stop polling and redirect to results
        if (pollingInterval) {
          clearInterval(pollingInterval);
          setPollingInterval(null);
        }
        navigate(`/results/${data.resultId}`);
      }
    },
    onAnalysisFailed: (data) => {
      if (data.jobId === jobId) {
        // Stop polling and show error
        if (pollingInterval) {
          clearInterval(pollingInterval);
          setPollingInterval(null);
        }
        setError(data.message || 'Analysis failed');
      }
    }
  });

  const checkStatus = async () => {
    try {
      const response = await apiService.getAssessmentStatus(jobId);

      if (response.success) {
        const statusData = response.data;
        setStatus(statusData);

        // If completed, redirect to results
        if (statusData.status === 'completed') {
          if (pollingInterval) {
            clearInterval(pollingInterval);
            setPollingInterval(null);
          }
          // Assuming we get resultId from status or use jobId
          navigate(`/results/${statusData.resultId || jobId}`);
        } else if (statusData.status === 'failed') {
          if (pollingInterval) {
            clearInterval(pollingInterval);
            setPollingInterval(null);
          }
          setError('Analysis failed. Please try again.');
        }
      }
    } catch (err) {
      console.error('Error checking status:', err);
      setError(err.response?.data?.message || 'Failed to check status');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (!jobId) {
      navigate('/assessment');
      return;
    }

    // Initial status check
    checkStatus();

    // Setup polling as fallback (every 5 seconds)
    const interval = setInterval(checkStatus, 5000);
    setPollingInterval(interval);

    // Cleanup
    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [jobId]);

  const getStatusColor = (status) => {
    switch (status) {
      case 'queued': return 'text-yellow-600';
      case 'processing': return 'text-blue-600';
      case 'completed': return 'text-green-600';
      case 'failed': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'queued':
        return (
          <div className="animate-pulse">
            <div className="h-8 w-8 bg-yellow-200 rounded-full"></div>
          </div>
        );
      case 'processing':
        return (
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        );
      case 'completed':
        return (
          <div className="h-8 w-8 bg-green-100 rounded-full flex items-center justify-center">
            <svg className="h-5 w-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
          </div>
        );
      case 'failed':
        return (
          <div className="h-8 w-8 bg-red-100 rounded-full flex items-center justify-center">
            <svg className="h-5 w-5 text-red-600" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </div>
        );
      default:
        return (
          <div className="h-8 w-8 bg-gray-200 rounded-full"></div>
        );
    }
  };

  if (isLoading && !status) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <LoadingSpinner size="xl" text="Loading Status..." />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md mx-auto">
        {/* Notifications */}
        {notifications.map((notification) => (
          <div key={notification.id} className={`mb-4 p-4 rounded-md ${
            notification.type === 'success' ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
          }`}>
            <div className="flex justify-between">
              <div>
                <h3 className={`text-sm font-medium ${
                  notification.type === 'success' ? 'text-green-800' : 'text-red-800'
                }`}>
                  {notification.title}
                </h3>
                <p className={`mt-1 text-sm ${
                  notification.type === 'success' ? 'text-green-700' : 'text-red-700'
                }`}>
                  {notification.message}
                </p>
              </div>
              <button
                onClick={() => clearNotification(notification.id)}
                className="text-gray-400 hover:text-gray-600"
              >
                ×
              </button>
            </div>
          </div>
        ))}

        {/* Status Card */}
        <div className="bg-white shadow rounded-lg p-6">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-6">
              Assessment Status
            </h1>
            
            {error ? (
              <ErrorMessage
                title="Assessment Error"
                message={error}
                onRetry={() => navigate('/assessment')}
                retryText="Start New Assessment"
              />
            ) : status ? (
              <div className="text-center">
                <div className="flex justify-center mb-4">
                  {getStatusIcon(status.status)}
                </div>
                
                <h2 className={`text-lg font-medium mb-2 ${getStatusColor(status.status)}`}>
                  {status.status.charAt(0).toUpperCase() + status.status.slice(1)}
                </h2>
                
                {status.progress && (
                  <div className="mb-4">
                    <ProgressBar
                      progress={status.progress}
                      color="blue"
                      label="Processing"
                      showPercentage={true}
                    />
                  </div>
                )}
                
                {status.estimatedTimeRemaining && (
                  <p className="text-sm text-gray-600 mb-4">
                    Estimated time remaining: {status.estimatedTimeRemaining}
                  </p>
                )}
                
                <div className="text-sm text-gray-500">
                  <p className="mb-2">Job ID: {jobId}</p>
                  <ConnectionStatus
                    isConnected={isConnected}
                    isAuthenticated={isAuthenticated}
                  />
                </div>
              </div>
            ) : null}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AssessmentStatus;
